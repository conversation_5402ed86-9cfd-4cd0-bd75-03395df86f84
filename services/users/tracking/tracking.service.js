"use strict";

const Model = require("./tracking.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const {MoleculerClientError} = require("moleculer").Errors;
const {WHITE_LIST} = require("./tracking.constants");
const User = require('../users.model');
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");


/**
 *
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "trackings",
  mixins: [Db<PERSON>ongoos<PERSON>(Model), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {
      "userId": 'users.get',
    },
    populateOptions: ["userId"],
  },
  hooks: {
    before: {
      "*": "checkPermissionActions",
    }
  },
  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    createTracking: {
      rest: {
        method: "POST",
        path: "/manager",
      },
      async handler(ctx) {
        try {
          const ips = ctx.meta.client_ip
          const userId = ctx.params.userId || ctx.meta.user?._id;
          ctx.params.ipAddress = ips;
          return await this.adapter.insert({...ctx.params, userId});

        } catch (e) {
          console.log(e);
        }
      },
    },
    getTracking: {
      rest: {
        method: "GET",
        path: "/manager",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {
            email,
            fullName,
            isDeveloper,
            onlyActiveUser,
            createdDateType,
            createdAtStart,
            createdAtEnd,
          } = ctx.params;
          if (!ctx.params.sort) ctx.params.sort = '-visitNumber';
          if (createdDateType) {
            ctx.params.time = createdDateType;
            ctx.params.fromDate = createdAtStart;
            ctx.params.toDate = createdAtEnd;
          }

          const queryTime = this.extractQueryTime(ctx.params);
          const paramsList = this.extractParamsList(ctx.params);
          const page = parseInt(paramsList.page);
          const pageSize = parseInt(paramsList.pageSize);
          const sort = paramsList.sort;

          let sortAggregate = {[sort]: 1};
          if (sort.indexOf('-') !== -1) {
            sortAggregate = {[sort.split('-')[1]]: -1};
          }
          const [trackingDataRes, projectDataRes, inputDataRes] = await Promise.allSettled([
            this.reportTracking({createdAt: queryTime.createdAt}),
            ctx.call("projects.statisticProject", {query: queryTime}),
            ctx.call("inputs.statisticInputTracking", {
              query: {
                createdAt: queryTime.createdAt,
                userId: {$exists: true}
              }
            }),
          ]);
          const projectData = projectDataRes.status === 'fulfilled' ? projectDataRes.value : [];
          const inputData = inputDataRes.status === 'fulfilled' ? inputDataRes.value : [];
          const trackingData = trackingDataRes.status === 'fulfilled' ? trackingDataRes.value : [];

          const queryUser = {
            isDeleted: false,
            active: true,
            ...(isDeveloper && {isDeveloper: isDeveloper === 'true' ? true : {$ne: true}}),
            ...(fullName ? {fullName: {$regex: fullName, $options: "i"}} : {}),
            ...(email ? {email: {$regex: email, $options: "i"}} : {}),
            ...(createdDateType ? queryTime : {})
          };

          const pagination = {
            page: page,
            pageSize: pageSize,
            sortAggregate: sortAggregate,
          };


          const userIds = await User.distinct("_id", queryUser);
          const userIdsSet = new Set(userIds.map(item => item.toString()));

          // Calculate Daily Active Users (DAU) and Monthly Active Users (MAU)
          const allUserVisited = trackingData.reduce((acc, item) => {
            if (item._id && userIdsSet.has(item._id.toString())) {
              acc.dau += item.visitDaysNumber;
              acc.mau.add(item._id.toString());
            }
            return acc;
          }, {dau: 0, mau: new Set()});

          const {$gte, $lte} = queryTime.createdAt;
          const totalDay = Math.ceil((queryTime.time === 'custom' ? $lte - $gte : new Date() - $gte) / (1000 * 60 * 60 * 24));

          const mau = allUserVisited.mau.size;
          const dau = allUserVisited.dau / totalDay;

          const [userList] = await this.trackingUserList(queryUser, trackingData, projectData, inputData, pagination, onlyActiveUser);

          return {
            users: userList,
            dau,
            mau,
          };
        } catch (e) {
          console.log(e);
        }
      },
    },
    downloadTracking: {
      rest: {
        method: "GET",
        path: "/manager/download",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          ctx.params.page = 1;
          ctx.params.limit = 1000000;
          const data = await this.actions.getTracking(ctx.params);
          console.log("getTracking=========", data.users.rows.length);

          // Create Excel file
          const excel = require('exceljs');
          const dayjs = require('dayjs');
          const workbook = new excel.Workbook();
          const worksheet = workbook.addWorksheet('User Tracking');

          // Define columns
          worksheet.columns = [
            {header: 'Full Name', key: 'fullName', width: 30},
            {header: 'Email', key: 'email', width: 30},
            {header: 'Created At', key: 'createdAt', width: 20},
            {header: 'Last Visit', key: 'lastVisit', width: 20},
            {header: 'Number of Visits', key: 'visitNumber', width: 15},
            {header: 'Number of Days Visited', key: 'visitDaysNumber', width: 20},
            {header: 'Number of Projects', key: 'numberOfProjects', width: 20},
            {header: 'Number of Submits', key: 'numberOfSubmits', width: 20},
            {header: 'Total Cost', key: 'totalCost', width: 15},
            {header: 'Is Developer', key: 'isDeveloper', width: 15},
            {header: 'IP Address', key: 'ipAddress', width: 20}
          ];

          // Format date columns using dayjs
          const dateFormat = (date) => {
            if (!date || date === 0) return '';
            return dayjs(date).format('YYYY/MM/DD HH:mm');
          };

          // Add data rows
          data.users.rows.forEach(user => {
            worksheet.addRow({
              ...user,
              createdAt: dateFormat(user.createdAt),
              lastVisit: dateFormat(user.lastVisit),
              totalCost: Math.round(user.totalCost * 1000) / 1000
            });
          });

          // Style the header row
          worksheet.getRow(1).font = {bold: true};

          // Set response headers for file download
          ctx.meta.$responseHeaders = {
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition': `attachment; filename=user-tracking-${dayjs().format('YYYY-MM-DD')}.xlsx`
          };

          // Write to buffer and return
          const buffer = await workbook.xlsx.writeBuffer();
          return buffer;

        } catch (error) {
          console.log(error);
          throw error;
        }
      }
    }
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {

    reportTracking(query) {
      return Model.aggregate([
        {$match: query},
        {
          $project: {
            createdAt: {
              $dateToString: {
                date: '$createdAt',
                format: '%Y-%m-%d',
                timezone: '+07:00',
              },
            },
            userId: 1,
            updatedAt: 1,
            ipAddress: 1,
          },
        },
        {
          $group: {
            _id: '$userId',
            visitNumber: {
              $sum: 1,
            },
            visitDays: {
              $addToSet: '$createdAt',
            },
            lastVisit: {$max: "$updatedAt"},
            ipAddress: {$last: "$ipAddress"},
          },
        },
        {
          $project: {
            userId: '$_id',
            visitNumber: 1,
            lastVisit: 1,
            ipAddress: 1,
            visitDaysNumber: {
              $function: {
                body: function (days) {
                  return days.length;
                },
                args: ['$visitDays'],
                lang: 'js',
              },
            },
          },
        },

      ]);
    },

    async trackingUserList(queryUser, trackingData, projectData, inputData, pagination, onlyActiveUser) {
      const {page, pageSize, sortAggregate} = pagination;
      delete queryUser.isDraft;
      return User.aggregate([
        {$match: queryUser},
        {
          $addFields: {
            trackings: {
              $first: {
                $filter: {
                  input: trackingData,
                  as: "tracking",
                  cond: {$eq: ["$$tracking._id", "$_id"]}
                }
              }
            },
            projects: {$first: {$filter: {input: projectData, as: "project", cond: {$eq: ["$$project._id", "$_id"]}}}},
            inputs: {$first: {$filter: {input: inputData, as: "input", cond: {$eq: ["$$input._id", "$_id"]}}}},
          }
        },
        {
          $project: {
            fullName: 1,
            email: 1,
            _id: 1,
            isDeveloper: 1,
            createdAt: 1,
            lastVisit: {$ifNull: ["$trackings.lastVisit", 0]},
            visitNumber: {$ifNull: ["$trackings.visitNumber", 0]},
            visitDaysNumber: {$ifNull: ["$trackings.visitDaysNumber", 0]},
            ipAddress: {$ifNull: ["$trackings.ipAddress", 'NA']},
            numberOfProjects: {$ifNull: ["$projects.numberProjects", 0]},
            numberOfSubmits: {$ifNull: ["$inputs.numberSubmit", 0]},
            toolUsed: {$ifNull: ["$inputs.toolUsed", []]},
            totalCost: {$ifNull: ["$inputs.totalCost", 0]},
          }
        },
        ...(onlyActiveUser === 'true' ? [{$match: {visitNumber: {$gt: 0}}}] : []),
        {$sort: sortAggregate,},
        {
          $facet: {
            rows: [
              {$skip: (page - 1) * pageSize},
              {$limit: pageSize}
            ],
            metadata: [
              {$count: "total"},
              {
                $addFields: {
                  page: page,
                  pageSize: pageSize,
                  totalPages: {$ceil: {$divide: ["$total", pageSize]}}
                }
              }
            ]
          }
        },
        {$replaceRoot: {newRoot: {$mergeObjects: ["$$ROOT", {$arrayElemAt: ["$metadata", 0]}]}}},
        {$unset: "metadata"},
      ]);
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
