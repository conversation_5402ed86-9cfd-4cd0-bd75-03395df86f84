"use strict";

const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {SPEAKING_PARTS} = require("../speaking.constants");
const {SPEAKING_QUESTIONS, SPEAKING_SESSIONS} = require("../../../constants/dbCollections");

// Schema for speaking questions
const schema = new Schema(
  {
    sessionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: SPEAKING_SESSIONS,
      required: true,
      index: true
    },
    part: {
      type: String,
      enum: Object.values(SPEAKING_PARTS),
      required: true,
      index: true
    },
    questionText: {
      type: String,
      required: true
    },
    partTopic: {
      type: String,
    },
    questionIndex: {
      type: Number
    },
    cueCard: {
      topic: String,
      suggestions: [String]
    },
    tags: [String],
    isDeleted: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Create indexes
schema.index({part: 1});

module.exports = mongoose.model(SPEAKING_QUESTIONS, schema, SPEAKING_QUESTIONS);
