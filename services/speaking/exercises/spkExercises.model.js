const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {SPEAKING_EXERCISES, FILE, USER} = require("../../../constants/dbCollections");
const {SPEAKING_PARTS} = require("../speaking.constants");

const schema = new Schema(
  {
    title: {
      type: String,
      required: true,
      maxlength: 100
    },
    topic: {
      type: String,
      required: true
    },
    avatarId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: FILE
    },
    status: {
      type: String,
      enum: ['draft', 'published', 'hidden'],
      default: 'draft'
    },
    parts: [
      {
        part: {
          type: String,
          enum: Object.values(SPEAKING_PARTS),
          required: true
        },
        topic: {type: String},
        cueCard: {
          text: String,
          audioId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: FILE,
          },
        },
        questions: [
          {
            text: {type: String, required: true},
            audioId: {
              type: mongoose.Schema.Types.ObjectId,
              ref: FILE,
            },
          },
        ],
      }
    ],

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      required: true
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER
    },
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER
    },
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model(SPEAKING_EXERCISES, schema, SPEAKING_EXERCISES);
