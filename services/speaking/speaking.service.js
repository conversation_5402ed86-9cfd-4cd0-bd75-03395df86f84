const sdk = require("microsoft-cognitiveservices-speech-sdk");
const path = require("path");
const fs = require("fs");
const wav = require("wav");
const {pronunciationAssessment} = require("./pronunciationAssessment");
const FileMixin = require("../../mixins/file.mixin");
const FunctionCommonMixin = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {getConfig} = require("../../config/config");
const {INPUT_TYPE} = require("../../constants/constant");
const config = getConfig(process.env.NODE_ENV);
const storageDir = path.join(__dirname, "storage");
const {MoleculerClientError} = require("moleculer").Errors;
const ffmpeg = require("fluent-ffmpeg");
const {getAudioDurationInSeconds} = require("get-audio-duration");
const globalState = new Map();
module.exports = {
  name: "speaking",
  mixins: [FileMixin, FunctionCommonMixin],

  actions: {
    getAudioUrl: {
      rest: 'GET /link/:fileName',
      async handler(ctx) {
        try {
          const {fileName} = ctx.params;
          let filePath = this.getFilePath(fileName, storageDir);
          if (!fs.existsSync(filePath)) {
            filePath = await this.broker.call("files.pathFromFileName", {fileName});
          }
          let stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            "Content-Type": "audio/wav",
            "Content-Length": stat.size,
            "Content-Disposition": `attachment;filename=${fileName}`
          };
          return fs.createReadStream(filePath, {});
        } catch (e) {
          throw new MoleculerClientError(i18next.t("error_file_not_found"), 404);
        }
      }
    },

  },

  methods: {
    async saveAudioFile(state, socket) {
      try {
        const {audioChunks, userId, inputData} = state;
        console.log("inputData", inputData);
        const {sessionId, questionIndex} = inputData;
        // Define file names
        const wavFileName = `record_audio_${sessionId}_${questionIndex}.wav`;
        const mp3FileName = `record_audio_${sessionId}_${questionIndex}.mp3`;

        const wavFilePath = this.getFilePath(wavFileName, storageDir);
        const mp3FilePath = this.getFilePath(mp3FileName, storageDir);

        // Create WAV file
        const fileStream = fs.createWriteStream(wavFilePath);
        const wavWriter = new wav.Writer({
          sampleRate: 16000,
          channels: 1,
          bitDepth: 16,
        });

        wavWriter.pipe(fileStream);
        audioChunks.forEach((chunk) => wavWriter.write(chunk));
        wavWriter.end();

        await new Promise((resolve, reject) => {
          fileStream.on("finish", resolve);
          fileStream.on("error", reject);
        });

        console.log(`WAV file saved: ${wavFilePath}`);

        // Convert WAV to MP3
        await new Promise((resolve, reject) => {
          ffmpeg(wavFilePath)
            .toFormat("mp3")
            .audioCodec("libmp3lame")
            .audioBitrate(128)
            .on("end", () => {
              console.log(`MP3 file saved: ${mp3FilePath}`);
              // Chỉ xóa file WAV khi chuyển đổi thành công
              try {
                fs.unlinkSync(wavFilePath);
              } catch (error) {
                console.error("Error deleting WAV file:", error);
                // Tiếp tục xử lý, không dừng ở đây nếu lỗi xóa file
              }
              resolve();
            })
            .on("error", (err) => reject(err))
            .save(mp3FilePath);
        });
        const [duration, file] = await Promise.all([
          getAudioDurationInSeconds(mp3FilePath),
          this.broker.call("resources.createFromRecordAudio", {
            audioChunks,
            userId,
            fileName: mp3FileName, // Save MP3 file info in DB
          }),
        ])

        // Send MP3 file URL via socket
        const audioUrl = `${config.domain}/api/microsoft/link/${mp3FileName}`;

        if (globalState[socket.id]) {
          globalState[socket.id].fileId = file._id;
          globalState[socket.id].audioDuration = duration;
        }

        socket.send({state: "audio_file_saved", audioUrl, fileId: file._id});
      } catch (e) {
        console.error("Error in saveAudioFile:", e);
        socket.send({state: "error", message: "An error occurred while saving the audio file."});
      }
    },

    async handleFeedback(inputData, socket, connectionId) {
      try {
        await this.saveSessionData(inputData, connectionId);
        socket.emit("finish-recognition");
      } catch (error) {
        console.error("Error handling feedback:", error);
        socket.send({state: "error", message: "An error occurred while processing feedback."});
      }
    },
    async getContentScore(data) {
      try {
        const {topic, recognizedText} = data

        if (!topic || !recognizedText) {
          this.logger.error("Missing topic or recognizedText in getContentScore");
          return {
            vocabularyScore: 0,
            grammarScore: 0,
            topicScore: 0
          };
        }

        const prompt = `**Scoring criteria:**

          **Grammar Score (1–100):** Assesses grammatical accuracy, focusing on whether errors significantly impede understanding.

          * **80–100:** Mostly correct grammar, minor errors that do not affect understanding (e.g. incorrect tense, missing article).

          * **60–79:** Noticeable grammatical errors, but meaning is clear (e.g. incorrect verb form, incomplete sentences).

          * **40–59:** Frequent grammatical errors, mildly impeding understanding.

          * **1–39:** Serious grammatical errors that significantly impede understanding.

          **Vocabulary Score (1–100):** Assesses vocabulary, focusing on whether the user can communicate effectively, even with simple words.

          * **80–100:** Good vocabulary, moderately varied, and includes relevant, though not advanced, vocabulary (e.g., "solar energy" for renewable energy).

          * **60–79:** Mostly basic vocabulary, low variety, but appropriate and conveys the message.

          * **40–59:** Very basic vocabulary, repetitive, but still conveys the idea.

          * **1–39:** Extremely limited vocabulary, difficult to understand the message, or frequent misuse of words.

          **Topic Score (1–100):** Assesses how closely the transcript sticks to the topic, focusing on effort and relevance rather than depth.

          * **80–100:** Transcript is on-topic, clearly focused, and has relevant main ideas, although not in-depth.

          * **60–79:** Transcript is mostly on-topic, but may be slightly off-topic or lack detail.

          * **40–59:** Transcript mentions topic but is significantly off-topic or has very little relevant content.

          * **1–39:** Transcript is mostly off-topic or has little relevant content.

          **Input:**

          Topic: ${topic}
          Voice transcript: ${recognizedText}`

        const messages = [
          {
            "role": "system",
            "content": "You are a language assessment AI, tasked with scoring the user's voice transcript based on a given topic. Evaluate the transcript and provide three scores from 1 to 100: Grammar Score, Vocabulary Score, and Topic Score."
          },
          {
            "role": "user",
            "content": prompt
          }
        ]
        const schema = {
          type: "object",
          properties: {
            vocabularyScore: {
              type: "number",
              description: "The vocabulary score"
            },
            grammarScore: {
              type: "number",
              description: "The grammar score"
            },
            topicScore: {
              type: "number",
              description: "The topic score"
            }
          },
          required: ["vocabularyScore", "grammarScore", "topicScore"],
        };

        try {
          return await this.broker.call("tools.submitFastLLM", {
            messages,
            temperature: 1,
            max_tokens: 100,
            responseFormat: 'json_object',
            schema
          });
        } catch (gptError) {
          this.logger.error("GPT API error:", gptError);
          return {
            vocabularyScore: 0,
            grammarScore: 0,
            topicScore: 0
          };
        }

      } catch (error) {
        this.logger.error("Error in getContentScore:", error);
        return {
          vocabularyScore: 0,
          grammarScore: 0,
          topicScore: 0
        };
      }
    },

    async saveSessionData(inputData, connectionId) {
      try {
        const words = inputData.results
          .flatMap(item => item.NBest.flatMap(nBestItem => nBestItem.Words))
          .filter(item => item?.Word);
        const wordsAudio = []
        const wordsData = []
        words.forEach(item => {
          wordsAudio.push(
            this.broker.call("files.wordAudio", {word: item.Word})
          )
          wordsData.push(
            this.broker.call("ipa.getWordData", {word: item.Word})
          )
        })
        await Promise.allSettled(wordsAudio);
        const results = await Promise.allSettled(wordsData);

        const mapWords = results.reduce((map, item) => {
          if (item) {
            map[item.word] = item;
          }
          return map;
        }, {});

        inputData.results.forEach(item => {
          item.NBest.forEach(nBestItem => {
            nBestItem.Words?.forEach(word => {
              const wordData = mapWords[word.Word] || {};
              word.ipa = wordData.ipa;
              word.audioUrl = wordData.audioUrl;
            });
          });
        });

        const audioUrl = `${config.domain}/api/microsoft/link/record_audio_${globalState[connectionId]?.fileId}.mp3`
        const {part, questionText, questionIndex, cueCard, tags, partTopic} = inputData

        let question;
        try {
          question = await this.broker.call("spkquestions.insert", {
            entity: {
              sessionId: inputData?.sessionId,
              part,
              questionIndex,
              questionText,
              cueCard,
              tags,
              partTopic
            }
          });
        } catch (inputError) {
          this.logger.error("Error inserting input:", inputError);
          throw new Error("Failed to save input data");
        }
        const {vocabularyScore, grammarScore, pronScore, fluencyScore} = inputData
        const feedback = this.convertAzureScoreToIELTS(vocabularyScore, grammarScore, pronScore, fluencyScore);
        const newAnswer = {
          questionId: question._id,
          sessionId: inputData?.sessionId,
          userId: globalState[connectionId]?.userId,
          transcript: inputData?.recognizedText,
          results: inputData?.results,
          feedback,
          audioUrl,
          part,
        };

        let answer;
        try {
          answer = await this.broker.call("spkanswers.insert", {
            entity: newAnswer
          });
        } catch (responseError) {
          this.logger.error("Error inserting answer:", responseError);
          throw new Error("Failed to save answer data");
        }

        // Xóa dữ liệu từ globalState sau khi đã xử lý xong
        delete globalState[connectionId];

      } catch (error) {
        this.logger.error("Error in saveSessionData:", error);
        throw error; // Re-throw để hàm gọi có thể xử lý
      }
    },

  },

  async started() {

  },

  events: {
    speakingConnected: {
      async handler(socket) {
        const connectionId = socket.id;
        // Khởi tạo nếu chưa tồn tại
        if (!this.connectionState) {
          this.connectionState = {};
        }

        this.logger.info(`Client connected: ${connectionId}`);
        const {speechKey, serviceRegion, confidenceThreshold} = await this.broker.call("settings.findOne");
        const format = sdk.AudioStreamFormat.getWaveFormat(16000, 16, 1, sdk.AudioFormatTag.PCM);
        this.connectionState[connectionId] = {
          audioBufferQueue: [],
          audioChunks: [],
          audioStream: sdk.AudioInputStream.createPushStream(format),
          inputData: {},
          userId: null,
          startRecognition: false,
          isRecording: true,
          processInterval: null,
          callbackFunction: (data) => this.handleFeedback(data, socket, connectionId),
          contentFunction: (data) => this.getContentScore(data),
        };

        const state = this.connectionState[connectionId];
        globalState[connectionId] = state;
        state.processInterval = setInterval(() => {
          if (state.audioBufferQueue.length > 0) {
            while (state.audioBufferQueue.length > 0) {
              const audioData = state.audioBufferQueue.shift();
              try {
                state.audioStream.write(audioData);
              } catch (err) {
                console.error("Error writing audio data to stream:", err);
              }
            }

            if (!state.startRecognition) {
              pronunciationAssessment({
                socket, speechKey, serviceRegion, confidenceThreshold,
                audioStream: state.audioStream,
                inputData: state.inputData,
                callback: state.callbackFunction,
                contentFunction: state.contentFunction,

              });
              state.startRecognition = true;
            }
          } else if (!state.isRecording) {
            clearInterval(state.processInterval);
            state.audioStream.close();
            console.log(`Stopped processing for client: ${connectionId}`);
          }
        }, 500);

        socket.on("audio", (data) => {
          state.audioBufferQueue.push(data.buffer);
          state.audioChunks.push(data.buffer);
          state.inputData = data.inputData;
          state.userId = data.userId;
        });

        socket.on("close-recording", async () => {
          console.log(`Closing recording for client: ${connectionId}`);
          state.isRecording = false;
          await this.saveAudioFile(state, socket);
        });

        socket.on("disconnect", () => {
          this.logger.info(`Client disconnected: ${connectionId}`);
          clearInterval(state.processInterval);
          state.audioStream.close();
          delete this.connectionState[connectionId];
          delete globalState[connectionId];
        });

        // Gửi sự kiện server_ready để thông báo cho client rằng server đã sẵn sàng nhận dữ liệu
        socket.emit("server_ready");
      }
    }
  },

};
