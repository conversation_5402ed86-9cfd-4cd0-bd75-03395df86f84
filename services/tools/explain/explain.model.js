const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { EXPLAIN } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    name: { type: String, required: true },
    instruction: { type: String, required: true },
    systemPrompt: { type: String },
    schemaInstruction: { type: Schema.Types.Mixed },
    responseFormat: {
      type: String,
      enum: ["json_object", "markdown", "text"],
      default: "text"
    },
    explainType: {
      type: String,
      enum: ["idea", "find_vocabulary", "help_me_understand", "help_me_write"],
      required: true
    },
    taskType: {
      type: String,
      enum: ["task1", "task2", "general"],
      default: "general"
    },
    gptModel: { type: String, default: "gpt-4o-mini" },
    temperature: { type: Number, default: 0.7 },
    maxTokens: { type: Number },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  }
);

module.exports = mongoose.model(EXPLAIN, schema, EXPLAIN);
