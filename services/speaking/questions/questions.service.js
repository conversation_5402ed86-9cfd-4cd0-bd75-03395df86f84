"use strict";

const DbMongoose = require("../../../mixins/dbMongo.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const QuestionModel = require("./questions.model");
const { SPEAKING_PARTS, QUESTIONS_COUNT } = require("../speaking.constants");
const { MoleculerClientError } = require("moleculer").Errors;
const i18next = require("i18next");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "spkquestions",
  mixins: [DbMongoose(QuestionModel), FunctionsCommon, AuthRole, BaseService, FileMixin],

  /**
   * Settings
   */
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    // Enable caching
    cache: {
      enabled: true,
      ttl: 60 * 30 // 30 minutes
    }
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {

  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {},

  /**
   * Service created lifecycle event handler
   */
  created() {},

  /**
   * Service started lifecycle event handler
   */
  async started() {},

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {}
};
