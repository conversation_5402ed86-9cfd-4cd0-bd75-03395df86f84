# Marketing Users Service

Dịch vụ quản lý danh sách người dùng cho Email Marketing, hỗ trợ upload file Excel và quản lý người dùng theo nhóm marketing.

## Tính năng chính

### 1. Upload file Excel
- Hỗ trợ định dạng `.xlsx`
- Validate dữ liệu trước khi import
- Kiểm tra duplicate email trong cùng marketing group
- Trả về thống kê import chi tiết

### 2. Quản lý người dùng
- L<PERSON>y danh sách người dùng theo marketing group
- Xóa người dùng khỏi marketing group (soft delete)
- Thống kê số lượng người dùng theo group

### 3. Template Excel
- Download template Excel mẫu
- Hướng dẫn format dữ liệu cần thiết

## Cấu trúc dữ liệu

### Marketing User Model
```javascript
{
  email: String (required, unique per group),
  name: String (required),
  phone: String (required),
  mktGroupId: ObjectId (required, ref: EmailGroups),
  isDeleted: <PERSON><PERSON><PERSON> (default: false),
  createdAt: Date,
  updatedAt: Date
}
```

### Validation Rules
- **Email**: Phải đúng format email và unique trong cùng marketing group
- **Name**: Bắt buộc, không được để trống
- **Phone**: Bắt buộc, format số điện thoại (10-15 ký tự)

## API Endpoints

### 1. Upload Excel File
```
POST /upload/marketingUsers
Content-Type: multipart/form-data

Parameters:
- file: Excel file (.xlsx)
- mktGroupId: ID của marketing group

Response:
{
  "total": 100,
  "success": 85,
  "failed": 10,
  "duplicate": 5,
  "errors": [
    {
      "row": 15,
      "error": "Invalid email format"
    }
  ]
}
```

### 2. Lấy danh sách người dùng theo group
```
GET /api/mktusers/:mktGroupId/users?page=1&pageSize=10

Response:
{
  "docs": [...],
  "totalDocs": 100,
  "page": 1,
  "limit": 10,
  "totalPages": 10
}
```

### 3. Xóa người dùng
```
DELETE /api/mktusers/:id

Response:
{
  "_id": "...",
  "isDeleted": true
}
```

### 4. Thống kê group
```
GET /api/mktusers/:mktGroupId/stats

Response:
{
  "mktGroupId": "...",
  "totalUsers": 150
}
```

### 5. Download template Excel
```
GET /api/mktusers/template

Response: Excel file download
```

## Format file Excel

### Cột bắt buộc:
1. **email** - Địa chỉ email (unique per group)
2. **name** - Tên người dùng
3. **phone** - Số điện thoại

### Ví dụ:
| email | name | phone |
|-------|------|-------|
| <EMAIL> | John Doe | +1234567890 |
| <EMAIL> | Jane Smith | +0987654321 |

## Xử lý lỗi

### Các lỗi thường gặp:
- **Missing required fields**: Thiếu email, name hoặc phone
- **Invalid email format**: Email không đúng định dạng
- **Invalid phone format**: Số điện thoại không đúng định dạng
- **Email already exists**: Email đã tồn tại trong marketing group
- **Marketing group not found**: Không tìm thấy marketing group

### Response lỗi:
```javascript
{
  "success": false,
  "message": "Error message",
  "code": 400,
  "data": {}
}
```

## Sử dụng trong code

### Upload file Excel:
```javascript
// Frontend - FormData
const formData = new FormData();
formData.append('file', excelFile);
formData.append('mktGroupId', groupId);

const response = await fetch('/upload/marketingUsers', {
  method: 'POST',
  body: formData,
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### Lấy danh sách users:
```javascript
const users = await broker.call('mktusers.getUsersByGroup', {
  mktGroupId: 'group-id',
  page: 1,
  pageSize: 20
});
```

### Xóa user:
```javascript
await broker.call('mktusers.deleteUser', {
  id: 'user-id'
});
```

## Testing

Chạy test:
```bash
npm test -- test/email-marketing/marketing-users.test.js
```

Test coverage:
- Upload và xử lý file Excel
- Validation dữ liệu
- CRUD operations
- Error handling

## Lưu ý quan trọng

1. **Unique constraint**: Email phải unique trong cùng marketing group, nhưng có thể trùng giữa các group khác nhau
2. **Soft delete**: Khi xóa user, chỉ đánh dấu `isDeleted = true`
3. **File cleanup**: File Excel upload sẽ được tự động xóa sau khi xử lý
4. **Memory usage**: Với file Excel lớn, cần chú ý memory usage
5. **Authentication**: Tất cả endpoints đều yêu cầu authentication
