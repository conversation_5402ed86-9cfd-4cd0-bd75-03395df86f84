const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./marketingUsers.model');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const FileMixin = require('../../../mixins/file.mixin');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

module.exports = {
  name: 'mktusers',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, FileMixin],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      'mktGroupId': 'mktgroups.get',
    },
  },

  hooks: {},

  actions: {
    /**
     * Upload Excel file and import marketing users
     */
    uploadExcel: {
      auth: "required",
      async handler(ctx) {
        const {filename} = ctx.meta;
        const {mktGroupId, folder} = ctx.meta.$multipart;
        
        if (!mktGroupId) {
          throw new MoleculerClientError('mktGroupId is required', 400);
        }

        // Verify that the marketing group exists
        const group = await this.broker.call('mktgroups.get', {id: mktGroupId});
        if (!group) {
          throw new MoleculerClientError('Marketing group not found', 404);
        }

        // Save uploaded file
        const filePath = this.getFilePath(filename, this.getDirPath(folder, this.getStoragePath()));
        await this.save(ctx.params, filename, folder);

        try {
          // Process Excel file
          const result = await this.processExcelFile(filePath, mktGroupId);
          
          // Clean up uploaded file
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }

          return result;
        } catch (error) {
          // Clean up uploaded file on error
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
          throw error;
        }
      }
    },

    /**
     * Get users by marketing group ID
     */
    getUsersByGroup: {
      rest: {
        method: 'GET',
        path: '/:mktGroupId/users'
      },
      params: {
        mktGroupId: {type: 'string'},
        page: {type: 'number', optional: true, default: 1},
        pageSize: {type: 'number', optional: true, default: 10},
      },
      async handler(ctx) {
        const {mktGroupId, page, pageSize} = ctx.params;

        const options = {
          page: page,
          limit: pageSize,
          populate: ['mktGroupId'],
          sort: {createdAt: -1}
        };

        const query = {
          mktGroupId: mktGroupId,
          isDeleted: false
        };

        return await this.adapter.paginate(query, options);
      }
    },

    /**
     * Delete user from marketing group
     */
    deleteUser: {
      rest: {
        method: 'DELETE',
        path: '/:id'
      },
      params: {
        id: {type: 'string'}
      },
      async handler(ctx) {
        const {id} = ctx.params;
        
        const user = await this.adapter.findById(id);
        if (!user) {
          throw new MoleculerClientError('Marketing user not found', 404);
        }

        // Soft delete
        return await this.adapter.updateById(id, {isDeleted: true});
      }
    },

    /**
     * Get statistics for a marketing group
     */
    getGroupStats: {
      rest: {
        method: 'GET',
        path: '/:mktGroupId/stats'
      },
      params: {
        mktGroupId: {type: 'string'}
      },
      async handler(ctx) {
        const {mktGroupId} = ctx.params;

        const totalUsers = await this.adapter.count({
          mktGroupId: mktGroupId,
          isDeleted: false
        });

        return {
          mktGroupId,
          totalUsers
        };
      }
    },

    /**
     * Download Excel template for user import
     */
    downloadTemplate: {
      rest: {
        method: 'GET',
        path: '/template'
      },
      async handler(ctx) {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Marketing Users Template');

        // Add headers
        const headers = ['email', 'name', 'phone'];
        worksheet.addRow(headers);

        // Add sample data
        worksheet.addRow(['<EMAIL>', 'John Doe', '+1234567890']);
        worksheet.addRow(['<EMAIL>', 'Jane Smith', '+0987654321']);

        // Style the header row
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' }
        };

        // Auto-fit columns
        worksheet.columns.forEach(column => {
          column.width = 20;
        });

        // Set response headers for file download
        ctx.meta.$responseHeaders = {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': 'attachment; filename=marketing-users-template.xlsx'
        };

        // Write to buffer and return
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
      }
    }
  },

  methods: {
    /**
     * Process Excel file and import users
     */
    async processExcelFile(filePath, mktGroupId) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(filePath);
      
      const worksheet = workbook.getWorksheet(1); // Get first worksheet
      if (!worksheet) {
        throw new MoleculerClientError('Excel file is empty or invalid', 400);
      }

      // Get header row to find column indices
      const headerRow = worksheet.getRow(1);
      const headers = {};
      headerRow.eachCell((cell, colNumber) => {
        const value = cell.value?.toString().toLowerCase().trim();
        if (value === 'email') headers.email = colNumber;
        if (value === 'name') headers.name = colNumber;
        if (value === 'phone') headers.phone = colNumber;
      });

      // Validate required columns exist
      if (!headers.email || !headers.name || !headers.phone) {
        throw new MoleculerClientError(
          'Excel file must contain columns: email, name, phone', 
          400
        );
      }

      const results = {
        total: 0,
        success: 0,
        failed: 0,
        duplicate: 0,
        errors: []
      };

      // Process each row (skip header)
      for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
        const row = worksheet.getRow(rowNumber);
        
        // Skip empty rows
        if (!row.hasValues) continue;

        results.total++;

        try {
          const email = row.getCell(headers.email).value?.toString().trim();
          const name = row.getCell(headers.name).value?.toString().trim();
          const phone = row.getCell(headers.phone).value?.toString().trim();

          // Validate required fields
          if (!email || !name || !phone) {
            results.failed++;
            results.errors.push({
              row: rowNumber,
              error: 'Missing required fields (email, name, or phone)'
            });
            continue;
          }

          // Validate email format
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            results.failed++;
            results.errors.push({
              row: rowNumber,
              error: 'Invalid email format'
            });
            continue;
          }

          // Check for duplicate in the same group
          const existingUser = await this.adapter.findOne({
            email: email,
            mktGroupId: mktGroupId,
            isDeleted: false
          });

          if (existingUser) {
            results.duplicate++;
            results.errors.push({
              row: rowNumber,
              error: 'Email already exists in this marketing group'
            });
            continue;
          }

          // Insert user
          await this.adapter.insert({
            email,
            name,
            phone,
            mktGroupId
          });

          results.success++;

        } catch (error) {
          results.failed++;
          results.errors.push({
            row: rowNumber,
            error: error.message
          });
        }
      }

      return results;
    }
  },

  events: {}
};
