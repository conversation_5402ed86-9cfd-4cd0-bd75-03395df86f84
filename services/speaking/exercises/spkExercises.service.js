"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const Model = require("./spkExercises.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const path = require("path");
const fs = require("fs");
const storageDir = path.join(__dirname, "storage");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "spkexercises",
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin, AuthRole],

  settings: {
    entityValidator: {},
    populates: {
      "avatarId": 'files.get',
      "createdBy": 'users.get',
      "updatedBy": 'users.get',
    },
    populateOptions: ["createdBy", "updatedBy", "avatarId"],
  },
  dependencies: [],

  actions: {

    createAudioFromText: {
      rest: "POST /textToSpeech",
      async handler(ctx) {
        const {text, voice, speed} = ctx.params;


        const audio = await this.broker.call("tts.textToSpeech", {text, voice, speed});
        const buffer = Buffer.from(await audio.arrayBuffer());
        const file = await ctx.call("files.createFromAudioBuffer", {buffer, folder: "dictation_shadowing"});

        const filePath = await ctx.call("files.filePath", {id: file._id});
        const transcript = await this.broker.call("whisper.transcriptAudio", {
          audioPath: filePath,
        });

        return {
          audioId: file._id,
          text: transcript.text,
        };
      }
    },

    createExercise: {
      rest: "POST /createExercise",
      async handler(ctx) {
        const {title, topic, status, parts, avatarId} = ctx.params;
        const user = ctx.meta.user;
        if (!user || !user.isSystemAdmin) {
          throw new MoleculerClientError(i18next.t("error_permission_denied"), 403);
        }
        return this.adapter.insert({title, topic, status, parts, avatarId, createdBy: user._id, updatedBy: user._id});
      },
    },
    // Cập nhật bài tập
    updateExercise: {
      rest: "PUT /:id/updateExercise",
      async handler(ctx) {

        const {id, ...updateData} = ctx.params;
        const user = ctx.meta.user;

        if (!user?.isSystemAdmin) {
          throw new Error("Permission denied");
        }

        const exercise = await this.adapter.findById(id);
        if (!exercise) {
          throw new MoleculerClientError(i18next.t("error_exercise_not_found"), 404);
        }

        Object.assign(exercise, updateData, {updatedBy: user._id});
        await exercise.save();

        return exercise;
      }


    },

    // Xóa bài tập (xóa mềm)
    deleteExercise: {
      rest: "DELETE /:id/deleteExercise",
      async handler(ctx) {

        const {id} = ctx.params;
        const user = ctx.meta.user;

        if (!user?.isSystemAdmin) {
          throw new Error("Permission denied");
        }

        const exercise = await this.adapter.findById(id);
        if (!exercise) {
          throw new MoleculerClientError(i18next.t("error_exercise_not_found"), 404);
        }

        exercise.isDeleted = true;
        await exercise.save();
        return exercise

      },
    },

  },

  events: {},

  methods: {
    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
  },

  created() {
  },

  async started() {
    this.createFolderIfNotExist(storageDir);
  },

  async stopped() {
  },
};
