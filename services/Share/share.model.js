const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { SHARE, FOLDER, PROJECT, USER, WORKSPACE } = require("../../constants/dbCollections");

const shareSchema = new Schema(
  {
    type: {
      type: String,
      enum: ["FOLDER", "PROJECT", "WORKSPACE"],
    },
    userId: { type: Schema.Types.ObjectId, ref: USER },
    folderId: { type: Schema.Types.ObjectId, ref: FOLDER },
    projectId: { type: Schema.Types.ObjectId, ref: PROJECT },
    workspaceId: { type: Schema.Types.ObjectId, ref: WORKSPACE },
    permission: {
      type: String,
      enum: ["EDITOR", "VIEWER"],
    },
    isGeneral: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);


module.exports = mongoose.model(SHARE, shareSchema, SHARE);
