const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./transaction.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

module.exports = {
  name: "transactions",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      customerId: "customers.get",
      subscriptionId: "subscriptions.get",
      packageId: "packages.get",
      discountIds: "discounts.get",
      promotionId: "promotions.get",
    },
    populateOptions: ["customerId", "subscriptionId.packageId", "discountIds", "promotionId", "packageId"],
  },

  hooks: {},

  actions: {
    paymentHistory: {
      rest: "GET /paymentHistory",
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const customer = await ctx.call("customers.getOneByUser", {userId: user._id});
        return await ctx.call("transactions.find", {query: {customerId: customer._id}, sort: "-createdAt"});
      },
    },
    getPaymentHistoryByUserId: {
      rest: "GET /:userId/paymentHistory",
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {userId} = ctx.params;
        const customer = await ctx.call("customers.getOneByUser", {userId});
        return await ctx.call("transactions.find", {query: {customerId: customer._id}, sort: "-createdAt"});
      },
    },
    testPaymentDone: {
      rest: "POST /testPaymentDone",
      async handler(ctx) {
        const {transactionId} = ctx.params;
        const state = "done";
        const responseCode = "01";
        const responseMessage = "oke";
        const transaction = await this.adapter.updateById(transactionId, {state, responseCode, responseMessage});
        if (state === "done") {
          this.broker.emit("discountsUsed", {
            discountIds: transaction.discountIds,
            customerId: transaction.customerId,
          });
          const transactionTransformed = await this.transformDocuments(
            ctx,
            {populate: this.settings.populateOptions},
            transaction,
          );
          if (
            (transactionTransformed.packageId && transactionTransformed.packageId.type === "base") ||
            transactionTransformed.packageId.customerTarget === "student"
          ) {
            await this.broker.emit("subscriptionUpdateStatus", {
              subscriptionId: transaction.subscriptionId._id.toString(),
              customer: transactionTransformed.customerId,
            });
          } else {
            await this.broker.emit("addOnPackageOrdered", {
              packages: transactionTransformed.packageId,
              customer: transactionTransformed.customerId,
            });
          }
        }

        this.broker.emit(`sse.${transactionId}`, transaction);
      },
    },
  },
  methods: {},
  events: {
    listenCreateTransaction: {
      params: {
        transactionId: "string",
      },
      async handler(context) {
        const {transactionId} = context.params;
        const transaction = await this.adapter.findById(transactionId);
        context.emit(`sse.${transactionId}`, transaction);
      },
    },
    transactionUpdateState: {
      params: {
        state: "string",
        transactionId: "string",
        // responseCode: "string",
        // responseMessage: "string",
      },
      async handler(context) {
        const {state, transactionId, responseCode, responseMessage} = context.params;
        const transaction = await this.adapter.updateById(transactionId, {state, responseCode, responseMessage});
        if (state === "done") {
          this.broker.emit("discountsUsed", {
            discountIds: transaction.discountIds,
            customerId: transaction.customerId,
          });
          const transactionTransformed = await this.transformDocuments(
            context,
            {populate: this.settings.populateOptions},
            transaction,
          );
          if (
            (transactionTransformed.packageId && transactionTransformed.packageId.type === "base") ||
            transactionTransformed.packageId.customerTarget === "student"
          ) {
            console.log("transactionTransformed", transactionTransformed);
            await this.broker.emit("subscriptionUpdateStatus", {
              subscriptionId: transaction.subscriptionId._id.toString(),
              customer: transactionTransformed.customerId,
            });
          } else {
            await this.broker.emit("addOnPackageOrdered", {
              packages: transactionTransformed.packageId,
              customer: transactionTransformed.customerId,
            });
          }
        }

        this.broker.emit(`sse.${transactionId}`, transaction);
      },
    },
  },
  created() {},

  /**
   * Service started lifecycle event handler
   */
  async started() {},

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {},

  async afterConnected() {},
};
