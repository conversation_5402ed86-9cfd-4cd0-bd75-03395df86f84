const ShareModel = require("./share.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const { PERMISSION_ACCESS } = require("../../constants/constant");
const i18next = require("i18next");
const { MoleculerClientError } = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "share",
  mixins: [DbMongoose(ShareModel), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {
      "folderId": 'folders.get',
      "projectId": 'projects.get',
      "userId": 'users.get',
    },
    populateOptions: ["folderId.ownerId", "projectId.ownerId", "userId"]
  },

  hooks: {
    before: {
      async getAllWithoutPagination(ctx, res) {
        ctx.params.query = JSON.stringify({
          ...JSON.parse(ctx.params.query),
          isGeneral: { $ne: true },
        });
        return res;
      },
      "project|folder": "checkPermission",
      "project|folder|unshare": "checkProjectOrFolderExist",
    },
    after: {
      "*": "activityLogger"
    }
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL
    },
    sharedWithMe: {
      rest: {
        method: "GET",
        path: "/sharedWithMe",
      },
      auth: "required",
      async handler(ctx) {
        const shares = await this.adapter.find({
          query: { userId: ctx.meta.user._id, isDeleted: false },
          sort: '-updatedAt'
        });

        const folderIds = shares.flatMap(share => share.folderId || []);
        const projectIds = shares.flatMap(share => share.projectId || []);

        const actions = [
          { action: 'projects.find', params: { query: { _id: { $in: projectIds } }, sort: '-updatedAt' } },
          { action: 'folders.find', params: { query: { _id: { $in: folderIds } }, sort: '-updatedAt' } },
        ];

        const [projects, folders] = await ctx.mcall(actions);

        const mcallActions = folders.map(folder => ({
          action: 'projects.count',
          params: { query: { folderId: folder._id, isDeleted: false } }
        }));

        const mcallRes = await ctx.mcall(mcallActions);

        folders.forEach((folder, index) => {
          folder.projects = mcallRes[index];
        });

        return { folders, projects };
      }
    },
    workspaceShared: {
      rest: "GET /workspaceShared",
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const userId = ctx.meta.user._id;

        const query = { userId, isDeleted: false, type: "WORKSPACE" };

        const shares = await this.adapter.find({ query });
        const workspaceIds = this.extractKeyFromList(shares, 'workspaceId');

        const findQuery = { _id: workspaceIds };

        const options = { sort: '-updatedAt' };

        return ctx.call('workspaces.find', { query: findQuery, ...options });
      }
    },
    removeShared: {
      rest: "DELETE /shared",
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { query } = ctx.params;
        const shareData = await this.adapter.find({ query });
        ctx.emit('share.removeShared', shareData);
        return this.adapter.removeMany({ _id: this.extractIdFromList(shareData) });
      }
    },
    project: {
      auth: "required",
      rest: 'POST /project',
      activityLogger: true,
      async handler(ctx) {
        const { data, projectId } = ctx.params;
        const { ownerId } = await ctx.call('projects.get', { id: projectId });

        await this.handleUpdateShares(ctx, ownerId);

        const dataToDelete = data
          .filter(share => share.isDeleted && share._id)
          .map(share => ({ projectId, userId: share.userId._id }));

        const mcallDeleteActions = dataToDelete.map(item => ({
          action: 'share.removeShared',
          params: { query: item },
        }));
        await ctx.mcall(mcallDeleteActions);

        return data.filter(row => row.userId);
      }
    },

    folder: {
      auth: "required",
      rest: 'POST /folder',
      activityLogger: true,
      async handler(ctx) {
        const { data, folderId } = ctx.params;

        const { ownerId } = await ctx.call('folders.get', { id: folderId });
        await this.handleUpdateShares(ctx, ownerId);

        const projects = await ctx.call('projects.find', { query: { folderId, isDeleted: false }, populate: [] });

        const mcallActions = projects.map(project => ({
          action: 'share.project',
          params: { data, projectId: project._id },
        }));

        ctx.mcall(mcallActions);

        const dataToDel = data
          .filter(share => !!share.isDeleted && !!share._id)
          .map(share => ({ folderId, userId: share.userId._id }));

        if (dataToDel.length > 0) {
          const mcallDeleteActions = dataToDel.map(item => ({
            action: 'share.removeShared',
            params: { query: item },
          }));
          await ctx.mcall(mcallDeleteActions);
        }

        return data.filter(row => !!row.userId);
      }
    },

    workspace: {
      auth: "required",
      rest: 'POST /workspace',
      // activityLogger: true,
      async handler(ctx) {
        const { data, workspaceId } = ctx.params;
        ctx.meta.skipPermissionCheck = true;

        await this.handleUpdateShares(ctx);

        const query = { workspaceId, isDeleted: false };

        const [projects, folders] = await Promise.all([
          ctx.call('projects.find', { query, populate: [] }),
          ctx.call('folders.find', { query, populate: [] })
        ]);

        const folderActions = folders.map(folder => ({
          action: 'share.folder',
          params: { data, folderId: folder._id }
        }));

        const projectActions = projects.map(project => ({
          action: 'share.project',
          params: { data, projectId: project._id }
        }));

        ctx.mcall([...folderActions, ...projectActions]);

        const dataToDel = data.filter(share => share.isDeleted && share._id);

        if (dataToDel.length > 0) {
          const idsToRemove = this.extractIdFromList(dataToDel);
          await this.adapter.removeMany({ _id: idsToRemove });
        }

        delete ctx.meta.skipPermissionCheck;
        return data;
      }
    },
    permissionAccess: {
      rest: {
        method: "GET",
        path: "/:id/permission",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { id } = ctx.params;
        const folder = await this.adapter.findOne({ _id: id });

        if (folder.ownerId.toString() === ctx.meta.user?._id.toString()) {
          return PERMISSION_ACCESS.OWNER;
        }

        const shared = await this.broker.call('share.find', {
          query: {
            folderId: id,
            isDeleted: false,
            userId: ctx.meta.user?._id
          }
        });

        if (shared.length === 0) {
          return PERMISSION_ACCESS.NO_PERMISSION;
        }

        return shared[0].permission;
      }
    },
    allSharedWithMe: {
      rest: {
        method: "GET",
        path: "/allSharedWithMe",
      },
      auth: "required",
      async handler(ctx) {
        const { limit } = ctx.params;
        const shares = await ctx.call("share.find", {
          query: { userId: ctx.meta.user?._id, isDeleted: false },
          sort: '-updatedAt'
        });
        const folderIds = shares.flatMap(({ folderId }) => folderId ? [folderId._id.toString()] : []);
        const projectIds = shares.flatMap(({ projectId }) => projectId ? [projectId._id.toString()] : []);
        const filteredShares = shares.filter(({ projectId, folderId }) => {
          return folderId || (projectId && !folderIds.includes(projectId?.folderId?.toString()));
        });

        const [folderStarred, projectStarred] = await Promise.all([
          ctx.call("mySaved.find", {
            query: { userId: ctx.meta.user?._id, folderId: { $in: folderIds } },
            sort: '-updatedAt', populate: [], fields: ['folderId', '_id'],
          }),
          ctx.call("mySaved.find", {
            query: { userId: ctx.meta.user?._id, projectId: { $in: projectIds } },
            sort: '-updatedAt', populate: [], fields: ['projectId', '_id'],
          })
        ]);

        const mapFolderStarred = Object.fromEntries(folderStarred.map(starred => [starred.folderId, starred]));
        const mapProjectStarred = Object.fromEntries(projectStarred.map(starred => [starred.projectId, starred]));
        for (const share of filteredShares) {
          if (share.folderId) {
            share.folderId.isSaved = !!mapFolderStarred[share.folderId._id];
          }
          if (share.projectId) {
            share.projectId.isSaved = !!mapProjectStarred[share.projectId._id];
          }
        }

        return !!limit ? filteredShares.slice(0, limit) : filteredShares;
      }
    },
    createGeneralAccess: {
      rest: {
        method: "POST",
        path: "/createGeneralAccess",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { folderId, userId, type, permission, isGeneral } = ctx.params;
        const project = await ctx.call("projects.find", {
          query: { folderId, isDeleted: false, isDraft: false },
        });
        const bulkWriteOperations = project.map(row => ({
          updateOne: {
            filter: { projectId: row._id, userId },
            update: {
              $set: {
                projectId: row._id,
                userId,
                type: "PROJECT",
                permission,
                isGeneral,
                isDeleted: false
              }
            },
            upsert: true,
          },
        }));

        await ShareModel.bulkWrite(bulkWriteOperations, { ordered: false });
        return await this.adapter.insert({ folderId, userId, type, permission, isGeneral });
      }
    },
    unshare: {
      rest: {
        method: "DELETE",
        path: "/unshare",
      },
      auth: "required",
      activityLogger: true,
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { projectId, userId, folderId } = ctx.params;
        return await this.adapter.removeMany({ projectId, folderId, userId });
      }
    },
    unshareGeneral: {
      rest: {
        method: "DELETE",
        path: "/unshareGeneral",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { projectId, folderId, isGeneral } = ctx.params;
        if (folderId) {
          const projects = await ctx.call('projects.find', {
            query: {
              folderId,
              isDeleted: false
            }
          });
          const promises = projects.map(project => {
            this.adapter.removeMany({ projectId: project._id, isGeneral });
          });
          promises.push(this.adapter.removeMany({ folderId, isGeneral }));
          return await Promise.all(promises);
        }

        return await this.adapter.removeMany({ projectId, folderId, isGeneral });
      }
    },
    unshareAllInWorkspace: {
      rest: {
        method: "DELETE",
        path: "/unshareWorkspace",
      },
      auth: "required",
      // activityLogger: true,
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { workspaceId, userId } = ctx.params;
        const folders = await ctx.call("folders.find", {
          query: { workspaceId },
        });
        const folderIds = folders.map(({ _id }) => _id);
        const projects = await ctx.call("projects.find", {
          query: { $or: [{ workspaceId }, { folderId: { $in: folderIds } }] },
        });
        const projectIds = projects.map(({ _id }) => _id);
        ctx.call("mySaved.removeMany", { userId, projectIds });
        ctx.call("mySaved.removeMany", { userId, folderIds });
        ctx.call("recents.removeMany", { userId, projectIds });
        return await Promise.all([
          await this.adapter.removeMany({ workspaceId, userId }),
          await this.adapter.removeMany({ projectId: projectIds, userId }),
          await this.adapter.removeMany({ folderId: folderIds, userId }),
        ]);

      }
    }

  },

  /**
   * Events
   */
  events: {
    // async "project.copied"(payload) {
    //   this.projectCopied(payload);
    // },
    async "project.created"(payload, sender, event, ctx) {
      this.projectCreated(payload, sender, event, ctx);
    },
    async "project.moved"(payload, sender, event, ctx) {
      this.projectMoved(payload, sender, event, ctx);
    },
    // async "folder.copied"(payload, sender, event) {
    //   this.logger.info("payload", payload, sender, event);
    //   const { oldFolderId, newFolder } = payload;
    //   const shares = await this.adapter.find({
    //     query: {
    //       folderId: oldFolderId,
    //       isDeleted: false,
    //       userId: { $ne: newFolder.ownerId }
    //     }
    //   });
    //   const dataUpsert = shares.map(share => {
    //     return {
    //       folderId: newFolder._id,
    //       type: share.type,
    //       userId: share.userId,
    //       permission: share.permission
    //     };
    //   });
    //   this.updateManyShares(dataUpsert);
    // },

    async "project.deleted"(payload) {
      this.adapter.removeMany({ projectId: payload });
    },
    async "folder.deleted"(payload) {
      this.adapter.removeMany({ folderId: payload });
    },
    userJoinedOrganization: {
      params: {
        user: "object",
      },
      async handler(ctx) {
        const { organizationId, user } = ctx.params;
        const workspace = await ctx.call('workspaces.find', {
          query: {
            organizationId,
          }
        });
        const data = [{
          email: user.email,
          permission: PERMISSION_ACCESS.VIEWER,
        }];
        await ctx.call('share.workspace', {
          data, workspaceId: workspace[0]._id
        });
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    async activityLogger(context, res) {
      const { action, params } = context;

      if (action.activityLogger && params?.workspaceId) {
        const projectId = params?.projectId;
        const project = projectId ? await context.call('projects.get', { id: params?.projectId }) : {};
        const folderId = params?.folderId || project?.folderId?._id || project?.folderId;

        const workspace = await context.call('workspaces.get', { id: params?.workspaceId.toString() });
        if (workspace.type === "ORGANIZATIONAL") {
          const data = params?.data;
          data.forEach((item, index) => {
            const metadata = {
              permission: params?.data[index]?.permission,
              email: params?.data[index]?.email || params?.data[index]?.userId?.email,
            };
            let action = "share";
            if (params?.data[index]?.userId?._id) {
              action = "unshare";
            } else {
              metadata.shareUserId = params?.data[index]?.userId;
            }

            context.emit('activities.logger', {
              metadata,
              projectId: projectId,
              folderId: folderId,
              action: action,
            });
          });
        }
      }
      return res;
    },

    async projectCopied(payload) {
      const { oldProject, newProject } = payload;

      const shares = await this.adapter.find({
        query: {
          projectId: oldProject._id,
          isDeleted: false,
          userId: { $ne: newProject.ownerId }
        }
      });

      const dataUpsert = shares.map(share => ({
        projectId: newProject._id,
        type: share.type,
        userId: share.userId,
        permission: share.permission
      }));

      const isOwnerChanged = oldProject?.ownerId?.toString() !== newProject?.ownerId?.toString();

      if (isOwnerChanged) {
        dataUpsert.push({
          projectId: newProject._id,
          type: 'PROJECT',
          userId: oldProject?.ownerId,
          permission: PERMISSION_ACCESS.EDITOR
        });
      }

      this.updateManyShares(dataUpsert);
    },

    async projectCreated(payload, sender, event, ctx) {
      const projectId = payload.projectId.toString();
      const project = await this.broker.call('projects.get', { id: projectId });

      if (project.folderId) {
        const folderId = project.folderId.toString();
        const folder = await this.broker.call('folders.get', { id: folderId });

        const query = {
          folderId,
          isDeleted: false,
          userId: { $ne: ctx.meta.user?._id }
        };
        const shares = await this.adapter.find({ query });

        const dataUpsert = shares.map(share => ({
          projectId,
          type: 'PROJECT',
          userId: share.userId,
          permission: share.permission
        }));

        if (folder.ownerId.toString() !== ctx.meta.user?._id.toString()) {
          dataUpsert.push({
            projectId,
            type: 'PROJECT',
            userId: folder.ownerId,
            permission: PERMISSION_ACCESS.EDITOR
          });
        }
        this.updateManyShares(dataUpsert);
      }
    },

    async handleUpdateShares(ctx, ownerId) {
      const { data, projectId, workspaceId, folderId } = ctx.params;

      const dataUpsert = data.filter((share) => !share.isDeleted);

      const emails = this.extractKeyFromList(dataUpsert, 'email');

      const users = await this.broker.call('users.find', { query: { email: emails } });

      const emailMap = this.convertObject(users, 'email');

      const updatedData = dataUpsert
        .map((item) => {
          const { email } = item;
          const userId = item.userId || emailMap[email]?._id;
          const type = folderId ? 'FOLDER' : projectId ? 'PROJECT' : 'WORKSPACE';
          return { ...item, userId, projectId, workspaceId, folderId, type, isGeneral: false };
        })
        .filter((item) => item?.userId?.toString() !== ownerId?.toString());

      this.updateManyShares(updatedData);
    },

    updateManyShares(data) {
      const bulkOps = data
        .filter((row) => !!row.userId)
        .map((row) => ({
          updateOne: {
            filter: { userId: row.userId, projectId: row.projectId, folderId: row.folderId },
            update: { $set: { ...row, isDeleted: false } },
            upsert: true,
          },
        }));

      ShareModel.bulkWrite(bulkOps);
    },
    async checkProjectOrFolderExist(ctx) {
      const { projectId, folderId } = ctx.params;

      const project = projectId ? await ctx.call('projects.get', { id: projectId }) : {};
      if (project.isDeleted === true) {
        throw new MoleculerClientError(i18next.t("project_was_deleted"), 404)
      }

      const folder = folderId ? await ctx.call('folders.get', { id: folderId }) : {};
      if (folder.isDeleted === true) {
        throw new MoleculerClientError(i18next.t("folder_was_deleted"), 404)
      }
    },
    async projectMoved(payload, sender, event, ctx) {
      const projectId = payload.projectId.toString();
      const project = await this.broker.call('projects.get', { id: projectId });

      if (project.folderId) {
        const folderId = project.folderId.toString();
        const folder = await this.broker.call('folders.get', { id: folderId });

        const query = {
          isDeleted: false,
          userId: { $nin: [ctx.meta.user?._id, project.ownerId] }
        };

        const shares = await this.adapter.find({ query: { ...query, folderId }});

        const dataUpsert = shares
          .map(share => ({
          projectId,
          type: 'PROJECT',
          userId: share.userId,
          permission: share.permission
        }));

        if(folder.ownerId.toString() !== project.ownerId.toString()) {
          dataUpsert.push({
            projectId,
            type: 'PROJECT',
            userId: folder.ownerId,
            permission: PERMISSION_ACCESS.EDITOR
          });
        }

        this.updateManyShares(dataUpsert);
      }
    },
    async checkPermission(ctx) {
      const {user, skipPermissionCheck} = ctx.meta;
      if(user?.isSystemAdmin || skipPermissionCheck) return;

      const { projectId, folderId } = ctx.params;
      const id = projectId || folderId;
      const entity = projectId ? 'projects' : 'folders';
      const permission = await ctx.call(`${entity}.permissionAccess`, { id });

      if (![PERMISSION_ACCESS.OWNER, PERMISSION_ACCESS.EDITOR].includes(permission)) {
        const errorKey = projectId ? "dont_have_permission_project" : "dont_have_permission_folder";
        throw new MoleculerClientError(i18next.t(errorKey), 403);
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
