"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const Model = require("./submissions.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "exercisesubmissions",
  mixins: [Db<PERSON>ongoose(Model), FunctionsCommon, BaseService, FileMixin, AuthRole],

  settings: {
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },

  dependencies: [],

  actions: {
    checkAnswerDictation: {
      rest: "POST /checkAnswerDictation",
      async handler(ctx) {
        const {segment, studentAnswer, exerciseId, mode, exerciseType = "dictation", submissionId} = ctx.params;
        if (!segment || !studentAnswer || !exerciseId) {
          throw new MoleculerClientError("Missing required parameters", 400, "MISSING_PARAMS", ["segment", "studentAnswer", "exerciseId"]);
        }
        const userId = ctx.meta.user?._id;

        const insertData = {
          segment,
          studentAnswer,
          exerciseId,
          studentId: userId,
          mode,
          exerciseType,
          errorLists: [],
          correctAnswer: mode === "word" ? segment.hiddenWord : segment.text
        };
        this.evaluateDictationAnswer(insertData, mode, segment, studentAnswer);
        if (submissionId) {
          const existingSubmission = await this.adapter.findById(submissionId);
          if (!existingSubmission) {
            throw new MoleculerClientError("Submission not found", 400);
          }
          return this.adapter.updateById(existingSubmission._id, insertData, {new: true});
        } else {
          return this.adapter.insert(insertData);
        }
      }
    },


    resetAnswerDictation: {
      rest: "POST /resetAnswerDictation",
      async handler(ctx) {
        const {submissionId} = ctx.params;
        const deletedData = await this.adapter.removeById(submissionId);
        if (!deletedData) {
          throw new MoleculerClientError("Submission not found", 400);
        }
        return {
          segment: deletedData?.segment,
          mode: deletedData?.mode
        }
      }
    },

    shadowingSubmission: {
      rest: "POST /shadowingSubmission",
      async handler(ctx) {
        const {
          accuracyScore,
          segment,
          studentAudioId,
          studentId,
          exerciseId,
          exerciseType = "shadowing",
          studentAnswer,
          standardResults,
          premiumResults
        } = ctx.params;
        const userId = ctx.meta.user?._id;

        const existingSubmission = await this.findExistingSubmission(userId, exerciseId, segment._id, null, exerciseType);

        const insertData = {
          segment,
          accuracyScore,
          exerciseId,
          studentId,
          studentAudioId,
          exerciseType,
          studentAnswer,
          standardResults,
          premiumResults
        };

        return existingSubmission
          ? this.adapter.updateById(existingSubmission._id, insertData)
          : this.adapter.insert(insertData);
      }
    },

    calculateShadowingCost: {
      rest: "GET /calculateShadowingCost",
      async handler(ctx) {
        try {
          const { query } = ctx.params; query.exerciseType = "shadowing";
          // Tạo query để lọc theo thời gian và exerciseType
          query.exerciseType = "shadowing"

          // Lấy danh sách các bài nộp shadowing
          const submissions = await this.adapter.find({query});

          // Lấy thông tin về giá của model gpt-4o-mini-transcribe
          const modelInfo = await this.broker.call("gptmodelprice.findOne", {gptModel: "gpt-4o-mini-transcribe"});

          if (!modelInfo) {
            throw new MoleculerClientError("Model price information not found", 400);
          }

          // tính toán chi phi
          const submissionCosts = await Promise.all(submissions.map(submission => this.calculateCostForShadowing(submission)));
          const totalCost = submissionCosts.reduce((acc, cost) => acc + cost, 0);

          return {
            numberSubmit: submissions.length,
            totalCost,
            toolName: "Shadowing"
          }
        } catch (error) {
          this.logger.error("Error calculating shadowing cost:", error);
          throw new MoleculerClientError("Error calculating shadowing cost", 500);
        }
      }
    },

    getStudentSubmissions: {
      rest: "GET /getStudentSubmissions",
      async handler(ctx) {
        const {studentId, exerciseId, exerciseType} = ctx.params;
        return this.adapter.find({query: {studentId, exerciseId, exerciseType}});
      }
    }
  },

  events: {},

  methods: {

    async calculateCostForShadowing(submission, modelPrice = 0.003) {
      const audioDuration = await this.broker.call("files.getAudioDuration", {id: submission.studentAudioId});
      return Number(audioDuration) * modelPrice / 60 || 0;
    },

    async findExistingSubmission(userId, exerciseId, segmentId, mode, exerciseType) {
      const query = {
        studentId: userId,
        exerciseId,
        exerciseType
      };

      if (mode) {
        query.mode = mode;
      }

      const submissions = await this.adapter.find({query});
      return submissions.find(item => item.segment._id.toString() === segmentId.toString());
    },

    evaluateDictationAnswer(insertData, mode, segment, studentAnswer) {
      // Loại bỏ dấu câu trong câu trả lời gồm các dấu , . ! ?

      const normalizeText = text => text.replace(/[.,!?]/g, ' ').toLowerCase().split(" ").filter(Boolean);
      const normalizeWord = text => text.replace(/[.,!?]/g, ' ').toLowerCase().trim();

      if (mode === "word") {
        const correctWords = normalizeWord(segment.hiddenWord);
        const answerWords = normalizeWord(studentAnswer);
        if (correctWords !== answerWords) {
          insertData.errorLists.push({word: segment.hiddenWord, correctWord: studentAnswer});
          insertData.result = "incorrect";
        } else {
          insertData.result = "correct";
        }
      } else if (mode === "sentence") {
        const correctWords = normalizeText(segment.text);
        const answerWords = normalizeText(studentAnswer);
        let isCorrect = true;

        correctWords.forEach((correctWord, index) => {
          const answerWord = answerWords[index] || "";
          if (correctWord !== answerWord) {
            isCorrect = false;
            insertData.errorLists.push({word: answerWord, correctWord});
          }
        });

        insertData.result = isCorrect ? "correct" : "incorrect";
      }
    }
  },

  created() {
  },

  async started() {
  },

  async stopped() {
  },
};
