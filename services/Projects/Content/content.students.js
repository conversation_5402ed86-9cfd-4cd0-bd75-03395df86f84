"use strict";
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const {DEFAULT_PROJECT_NAME, OUTPUT_TYPE, INPUT_TYPE} = require("../../../constants/constant");
module.exports = {
  actions: {
    submitSpeaking: {
      rest: {
        method: "POST",
        path: "/submitSpeaking",
      },
      toolPermission: true,
      async handler(ctx) {
        try {
          const {workspaceId, contentId, inputData} = ctx.params;

          if (!contentId) {
            throw new MoleculerClientError(i18next.t("content_id_required"), 400, "VALIDATION_ERROR");
          }

          const [content, responses] = await Promise.all([
            this.adapter.findById(contentId),
            ctx.call('responses.find', {query: {contentId, isActivate: true}}),
          ]);

          if (!content) {
            throw new MoleculerClientError(i18next.t("content_not_found"), 404, "NOT_FOUND");
          }

          if (!responses || responses.length === 0) {
            throw new MoleculerClientError(i18next.t("no_active_response"), 404, "NOT_FOUND");
          }

          const savedInput = responses[0]?.inputId;
          if (!savedInput) {
            throw new MoleculerClientError(i18next.t("input_not_found"), 404, "NOT_FOUND");
          }

          const savedResponse = await ctx.call('responses.update', {id: responses[0]._id, state: "processing"});

          const {user} = ctx.meta;
          const [permissionAccess, project] = await Promise.all([
            ctx.call('projects.permissionAccess', {id: content.projectId}),
            ctx.call('projects.get', {id: content.projectId.toString()}),
          ]);

          if (!this.editAccess(permissionAccess)) {
            throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403, "FORBIDDEN");
          }

          // Update project name if it's the default name
          if (project.projectName === DEFAULT_PROJECT_NAME.UNTITLED_SPEECH) {
            await ctx.call("projects.update", {id: content.projectId, projectName: savedInput.inputData.topic});
          }

          // Combine the original inputData with the new inputData
          const inputEntity = {
            ...savedInput,
            workspaceId: workspaceId,
            userId: user?._id,
            toolId: content.toolId,
            inputData: {...savedInput.inputData, ...inputData}
          };

          // Update the input with the new data
          await ctx.call('inputs.update', {id: savedInput._id, ...inputEntity});

          // Notify that the project was modified
          ctx.emit('lastModifiedProject', {id: content.projectId});

          // Submit the input for processing
          await ctx.call("contents.submitInput", {
            input: inputEntity,
            processingResponse: savedResponse,
            projectId: content.projectId
          });

          // Update recent projects and trigger resource update
          ctx.emit('recentProject', {projectId: content.projectId, userId: ctx.meta.user?._id});
          ctx.emit("resourceUpdate", {
            inputData: inputEntity.inputData,
            inputType: savedInput.inputType,
            projectId: content.projectId
          });

          // Trigger student submitted event for quota tracking
          await ctx.emit("studentSubmited", {inputType: savedInput.inputType, workspaceId});

          return {
            ...savedResponse,
            state: "processing",
            output: {
              text: "Hold on! We are processing"
            },
            outputType: OUTPUT_TYPE.PRON_FEEDBACK
          };
        } catch (error) {
          this.logger.error("Error in submitSpeaking:", error);
          throw error;
        }
      }
    },

    submitIeltsWriting: {
      rest: {
        method: "POST",
        path: "/submitIeltsWriting",
      },
      toolPermission: true,
      async handler(ctx) {
        const {workspaceId, contentId, inputData, inputType, projectName} = ctx.params;
        const content = await this.adapter.findById(contentId);
        const {user} = ctx.meta;
        const permissionAccess = await ctx.call('projects.permissionAccess', {id: content.projectId});
        if (!this.editAccess(permissionAccess)) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403, "FORBIDDEN");
        }

        const inputEntity = {
          workspaceId: workspaceId,
          inputData,
          inputType,
          userId: user?._id,
          toolId: content.toolId,
          contentId: content._id,
        };

        const savedInput = await ctx.call('inputs.insert', {entity: inputEntity});
        const processingResponse = {
          inputId: savedInput._id,
          contentId: content._id,
          toolId: content.toolId,
          output: {
            text: "Hold on! We are processing"
          },
          state: "processing",
          isActivate: true
        };
        const savedResponse = await ctx.call('responses.insert', {entity: processingResponse});
        ctx.call('responses.deactivate', {
          contentId: savedResponse.contentId,
          id: savedResponse._id
        });
        const projectEntity = projectName ? {id: content.projectId, projectName, isDraft: false} : {
          id: content.projectId,
          isDraft: false
        };
        await ctx.call("projects.update", projectEntity);
        ctx.emit('lastModifiedProject', {id: content.projectId});

        await ctx.call("contents.submitInput", {
          input: savedInput,
          processingResponse: savedResponse,
          projectId: content.projectId
        });

        ctx.emit('recentProject', {projectId: content.projectId, userId: ctx.meta.user?._id});
        ctx.emit("resourceUpdate", {inputData, inputType, projectId: content.projectId});
        ctx.emit("imageUpdateSubmit", {inputData});
        await ctx.emit("studentSubmited", {inputType, workspaceId});
        return ctx.call('responses.get', {id: savedResponse._id, populate: ['inputId']});
      }
    },

    generateTopic: {
      rest: {
        method: "POST",
        path: "/generateTopic",
      },
      async handler(ctx) {
        const {category = '', tag = '', inputType} = ctx.params;

        let prompt = `Generate a comprehensive IELTS Writing Task 2 topic`;
        if (inputType === INPUT_TYPE.STUDENT_SPEAKING) {
          prompt = `Generate a IELTS Speaking Part 2 topic`;
        }

        if (tag) {
          prompt += ` about ${tag}`;
        }

        if (category) {
          prompt += ` in ${category}`;
        }
        console.log("prompt", prompt)
        const messages = [
          {role: "system", content: prompt},
          {role: "user", content: "No captions, just answers"},
        ];

        const schema = {
          type: "object",
          properties: {
            topic: {
              type: "string",
              description: "The generated topic"
            },
          },
          required: ["topic"],
        };

        try {
          const result = await this.broker.call("tools.submitFastLLM", {
            messages,
            temperature: 1,
            max_tokens: 200,
            responseFormat: 'json_object',
            schema
          });
          return result.topic
        } catch (error) {
          console.error("Error in handler:", error.message);
        }
      }
    }
  },
  methods: {
    async checkStudentSubmitPermission(ctx) {
      try {
        const {input} = ctx.params;
        const inputType = ctx.params.inputType || (input && input.inputType);
        const userId = ctx.meta.user?._id;

        if (!userId) {
          throw new MoleculerClientError(i18next.t("user_not_authenticated"), 401, "UNAUTHORIZED");
        }

        if (!inputType) {
          throw new MoleculerClientError(i18next.t("input_type_required"), 400, "VALIDATION_ERROR");
        }

        // Lấy tất cả quyền của người dùng
        const permissions = await ctx.call("permissions.find", {query: {userId}});

        if (!permissions || permissions.length === 0) {
          this.logger.warn(`No permissions found for user ${userId}`);
          throw new MoleculerClientError(i18next.t("no_subscription_found"), 403, "FORBIDDEN");
        }

        // Lọc ra những quyền đang active
        const activePermissions = permissions.filter(item =>
          item.subscriptionId && item.subscriptionId.status === "ACTIVE"
        );

        if (activePermissions.length === 0) {
          this.logger.warn(`No active permissions found for user ${userId}`);
          throw new MoleculerClientError(i18next.t("no_active_subscription"), 403, "FORBIDDEN");
        }

        const isSpeaking = inputType === INPUT_TYPE.STUDENT_SPEAKING;
        const usedKey = isSpeaking ? "speakingUsed" : "writingUsed";
        const limitKey = isSpeaking ? "speakingLimit" : "writingLimit";

        // Tìm quyền có còn lượt sử dụng không
        const haveAccess = activePermissions.find(item => {
          const limit = Number(item.accessLimit[limitKey] || 0);
          const used = Number(item.accessLimit[usedKey] || 0);
          const startDate = item.subscriptionId && item.subscriptionId.startDate
            ? new Date(item.subscriptionId.startDate)
            : null;

          return limit > used && startDate && startDate <= new Date();
        });

        if (!haveAccess) {
          const actionType = isSpeaking ? "speaking" : "writing";
          this.logger.warn(`User ${userId} has reached their ${actionType} submission limit`);

          const msg = isSpeaking ? "submit_speaking_limited" : "submit_writing_limited";
          throw new MoleculerClientError(i18next.t(msg), 403, "LIMIT");
        }

        // Logging thông tin sử dụng
        this.logger.info(`User ${userId} has permission to submit ${inputType}. Current usage: ${haveAccess.accessLimit[usedKey]}/${haveAccess.accessLimit[limitKey]}`);

      } catch (error) {
        if (error instanceof MoleculerClientError) {
          throw error;
        }
        this.logger.error("Error in checkStudentSubmitPermission:", error);
        throw new MoleculerClientError(i18next.t("permission_check_error"), 500, "INTERNAL_ERROR");
      }
    },
  },
  events: {},
};
