"use strict";

const DbMongoose = require("../../../mixins/dbMongo.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const SessionModel = require("./sessions.model");
const {SPEAKING_PARTS, SESSION_STATUS, QUESTIONS_COUNT} = require("../speaking.constants");
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const path = require("path");
const fs = require("fs");
const storageDir = path.join(__dirname, "../storage");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "spksessions",
  mixins: [DbMongoose(SessionModel), FunctionsCommon, AuthRole, BaseService, FileMixin],

  /**
   * Settings
   */
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    // Enable caching
    cache: {
      enabled: true,
      ttl: 60 * 5 // 5 minutes
    }
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Create a new speaking session
     *
     * @returns {Object} Created session
     */
    create: {
      rest: {
        method: "POST",
        path: "/"
      },
      auth: "required",
      async handler(ctx) {
        try {
          const userId = ctx.meta.userID;

          // Create new session
          return await this.adapter.insert({
            userId,
            startTime: new Date(),
            status: SESSION_STATUS.IN_PROGRESS,
            ...ctx.params
          });
        } catch (error) {
          this.logger.error("Error creating speaking session:", error);
          throw new MoleculerClientError("Failed to create speaking session", 500, "CREATE_SESSION_ERROR");
        }
      }
    },

    /**
     * Complete a speaking session
     *
     * @param {String} id - Session ID
     * @returns {Object} Completed session with results
     */
    complete: {
      rest: {
        method: "PUT",
        path: "/:id/complete"
      },
      auth: "required",
      params: {
        id: {type: "string"}
      },
      async handler(ctx) {
        try {
          const {id} = ctx.params;
          const session = await this.adapter.findById(id);
          const answers = await ctx.call("spkanswers.find", {
            query: {
              sessionId: id,
              isDeleted: false
            },
          });
          // Calculate scores
          const resultSummary = this.calculateScores(session.part, answers);
          // Get feedback
          const messages = this.createMessages(answers);

          const aiResult = await this.broker.call("tools.submitFastLLM", {
            messages,
            temperature: 0.3,
            max_tokens: 1000,
          });
          //update  session

          return await this.adapter.updateById(id, {
            endTime: new Date(),
            status: SESSION_STATUS.COMPLETED,
            feedback: aiResult,
            resultSummary
          });

        } catch (error) {
          if (error instanceof MoleculerClientError) throw error;
          const {id} = ctx.params;
          return await this.adapter.updateById(id, {
            endTime: new Date(),
            status: SESSION_STATUS.ERROR,
          });
          this.logger.error("Error completing session:", error);
          throw new MoleculerClientError("Failed to complete session", 500, "COMPLETE_SESSION_ERROR");
        }
      }
    },

    /**
     * Get session details with answers
     *
     * @param {String} id - Session ID
     * @returns {Object} Session with answers
     */
    getDetails: {
      rest: {
        method: "GET",
        path: "/:id/details"
      },
      auth: "required",
      params: {
        id: {type: "string"}
      },
      async handler(ctx) {
        try {
          const {id} = ctx.params;
          const userId = ctx.meta.userID;

          // Get session
          const session = await this.adapter.findById(id);
          if (!session) {
            throw new MoleculerClientError(`Session not found: ${id}`, 404, "SESSION_NOT_FOUND");
          }

          const sessionTransformed = this.transformDocuments(ctx, {}, session)

          // Get answers for this session
          const answers = await ctx.call("spkanswers.find", {
            query: {
              sessionId: id,
              isDeleted: false
            },
          });
          // Get questions for this session
          const questions = await ctx.call("spkquestions.find", {
            query: {
              sessionId: id,
              isDeleted: false
            },
          });
          return {
            ...sessionTransformed,
            answers,
            questions
          };
        } catch (error) {
          if (error instanceof MoleculerClientError) throw error;

          this.logger.error("Error getting session details:", error);
          throw new MoleculerClientError("Failed to get session details", 500, "GET_SESSION_DETAILS_ERROR");
        }
      }
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    /**
     * Create folder if not exist
     *
     * @param {String} folderPath - Folder path
     */
    createFolderIfNotExist(folderPath) {
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, {recursive: true});
      }
    },
    createMessages(answers) {
      const messages = [
        {
          role: "system",
          content: "Bạn là một trợ lý AI đánh giá bài nói của người dùng. Bạn sẽ đánh giá bài nói của người dùng và trả về kết quả. Bạn đánh giá dựa theo transcript và điểm số được cung cấp."
        }
      ];

      const parts = [SPEAKING_PARTS.PART1, SPEAKING_PARTS.PART2, SPEAKING_PARTS.PART3];
      parts.forEach(part => {
        const partAnswers = answers.filter(answer => answer.part === part);
        if (partAnswers.length > 0) {
          let prompt = `## ${part} topics: ${partAnswers[0].partTopic}\n`;
          partAnswers.forEach(answer => {
            prompt += `Question: ${answer.questionText}\nAnswer: ${answer.transcript}\nVocabulary Score: ${answer.vocabularyScore}\nGrammar Score: ${answer.grammarScore}\nPronunciation Score: ${answer.pronScore}\nFluency Score: ${answer.fluencyScore}\n\n`;
          });
          messages.push({
            role: "user",
            content: `This is the user's voice transcript and scores for ${part}:\n${prompt}`
          });
        }
      });

      return messages;
    },

    calculateScores(part, answers) {
      const calculateAverage = (scores) => scores.reduce((a, b) => a + b, 0) / scores.length;

      const parts = [SPEAKING_PARTS.PART1, SPEAKING_PARTS.PART2, SPEAKING_PARTS.PART3, SPEAKING_PARTS.FULL_TEST];
      const scores = {};

      for (const partType of parts) {
        const filteredAnswers = answers.filter(answer => answer.part === partType);
        scores[partType] = {
          vocabulary: calculateAverage(filteredAnswers.map(answer => answer.feedback.vocabularyScore)),
          grammar: calculateAverage(filteredAnswers.map(answer => answer.feedback.grammarScore)),
          pronunciation: calculateAverage(filteredAnswers.map(answer => answer.feedback.pronScore)),
          fluency: calculateAverage(filteredAnswers.map(answer => answer.feedback.fluencyScore))
        };
      }

      if (part !== undefined && part !== SPEAKING_PARTS.FULL_TEST) {
        return {
          vocabularyScore: scores[part].vocabulary,
          grammarScore: scores[part].grammar,
          pronScore: scores[part].pronunciation,
          fluencyScore: scores[part].fluency,
          overall: this.calculateIELTSOverall(scores[part].vocabulary, scores[part].grammar, scores[part].pronunciation, scores[part].fluency)
        };
      }

      const weights = [1, 2, 3];
      const fullTestScores = parts.reduce((acc, partType, index) => {
        for (const key of Object.keys(scores[partType])) {
          acc[key] = (acc[key] || 0) + scores[partType][key] * weights[index];
        }
        return acc;
      }, {});

      const totalWeight = weights.reduce((a, b) => a + b, 0);

      return {
        avgVocabularyScore: fullTestScores.vocabulary / totalWeight,
        avgGrammarScore: fullTestScores.grammar / totalWeight,
        avgPronScore: fullTestScores.pronunciation / totalWeight,
        avgFluencyScore: fullTestScores.fluency / totalWeight,
        overall: this.calculateIELTSOverall(fullTestScores.vocabulary / totalWeight, fullTestScores.grammar / totalWeight, fullTestScores.pronunciation / totalWeight, fullTestScores.fluency / totalWeight)
      };
    }

  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    this.createFolderIfNotExist(storageDir);
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  }
};
